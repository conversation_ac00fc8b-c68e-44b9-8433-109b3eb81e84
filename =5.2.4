Collecting django
  Downloading django-4.2.23-py3-none-any.whl.metadata (4.2 kB)
Collecting asgiref<4,>=3.6.0 (from django)
  Using cached asgiref-3.9.1-py3-none-any.whl.metadata (9.3 kB)
Collecting sqlparse>=0.3.1 (from django)
  Using cached sqlparse-0.5.3-py3-none-any.whl.metadata (3.9 kB)
Collecting typing_extensions>=4 (from asgiref<4,>=3.6.0->django)
  Downloading typing_extensions-4.14.1-py3-none-any.whl.metadata (3.0 kB)
Downloading django-4.2.23-py3-none-any.whl (8.0 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 8.0/8.0 MB 11.5 MB/s eta 0:00:00
Using cached asgiref-3.9.1-py3-none-any.whl (23 kB)
Using cached sqlparse-0.5.3-py3-none-any.whl (44 kB)
Downloading typing_extensions-4.14.1-py3-none-any.whl (43 kB)
Installing collected packages: typing_extensions, sqlparse, asgiref, django

Successfully installed asgiref-3.9.1 django-4.2.23 sqlparse-0.5.3 typing_extensions-4.14.1
